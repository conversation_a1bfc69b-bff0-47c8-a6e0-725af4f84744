<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型名称" prop="categoryName">
        <el-input
          v-model="queryParams.categoryName"
          placeholder="请输入类型名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- 隐藏新增按钮 -->
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:demandcategory:add']"
        >新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:demandcategory:edit']"
        >修改</el-button>
      </el-col>
      <!-- 隐藏删除按钮 -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:demandcategory:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:demandcategory:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="categoryList"
      @selection-change="handleSelectionChange"
      row-key="categoryId"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="类型ID" align="center" prop="categoryId" width="100" />
      <el-table-column label="类型名称" align="center" prop="categoryName" />
      <el-table-column label="类型标识" align="center" prop="categoryCode" width="120" />
      <el-table-column label="简称" align="center" prop="categoryShortName" width="120" />
      <el-table-column label="图标" align="center" prop="categoryIcon" width="80">
        <template slot-scope="scope">
          <img v-if="scope.row.categoryIcon" :src="getIconUrl(scope.row.categoryIcon)" class="category-icon" alt="类型图标" />
          <span v-else class="no-icon">无图标</span>
        </template>
      </el-table-column>
      <el-table-column label="类型描述" align="center" prop="categoryDesc" show-overflow-tooltip />
      <el-table-column label="排序" align="center" prop="sortOrder" width="120">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.sortOrder"
            :min="0"
            size="mini"
            :controls="false"
            @change="handleSortChange(scope.row)"
            style="width: 80px;"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:demandcategory:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleFormConfig(scope.row)"
            v-hasPermi="['miniapp:demandcategory:edit']"
          >表单配置</el-button>
          <!-- 隐藏删除按钮 -->
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:demandcategory:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改需求类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型名称" prop="categoryName">
              <el-input v-model="form.categoryName" placeholder="请输入类型名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型标识" prop="categoryCode">
              <el-input
                v-model="form.categoryCode"
                placeholder="请输入类型标识代码，如：tech、business"
                :disabled="form.categoryId != null"
              />
              <div class="form-tip">
                <p>• 用于标识需求类型的唯一代码</p>
                <p>• 建议格式：tech、business、service等</p>
                <p v-if="form.categoryId == null">• 一旦设置后不可修改</p>
                <p v-else style="color: #f56c6c;">• 类型标识不可修改</p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型简称" prop="categoryShortName">
              <el-input v-model="form.categoryShortName" placeholder="请输入类型名称简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" placeholder="数字越小越靠前" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="类型图标" prop="categoryIcon">
              <ImageUpload
                v-model="form.categoryIcon"
                :limit="1"
                :fileSize="2"
                :isShowTip="true"
              />
              <div class="form-tip">
                <p>• 支持 jpg、png、gif 格式</p>
                <p>• 文件大小不超过 2MB</p>
                <p>• 建议尺寸 32x32 像素，保证清晰度</p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="类型描述" prop="categoryDesc">
              <Editor
                v-model="form.categoryDesc"
                :min-height="200"
                :height="300"
                placeholder="请输入类型描述..."
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 表单字段配置对话框 -->
    <el-dialog title="表单字段配置" :visible.sync="formConfigOpen" width="1400px" append-to-body>
      <div class="form-config-container">
        <!-- 左侧配置区域 -->
        <div class="config-area">
          <div class="config-header">
            <h4>字段配置</h4>
            <div class="header-actions">
              <el-button type="success" size="small" @click="addFormModule" icon="el-icon-folder-add">添加模块</el-button>
              <el-button type="primary" size="small" @click="addFormField" icon="el-icon-plus">添加字段</el-button>
            </div>
          </div>

          <!-- 预设模板 -->
          <div class="template-section">
            <el-select v-model="selectedTemplate" placeholder="选择预设模板" size="small" @change="applyTemplate">
              <el-option label="基础需求模板" value="basic" />
              <el-option label="技术需求模板" value="tech" />
              <el-option label="商务合作模板" value="business" />
            </el-select>
          </div>

          <!-- 模块和字段列表 -->
          <div class="modules-list">
            <div v-for="(module, moduleIndex) in formModulesList" :key="moduleIndex" class="module-item">
              <el-card shadow="hover" class="module-card">
                <!-- 模块头部 -->
                <div class="module-header">
                  <div class="module-title-section">
                    <!-- 模块图标 -->
                    <div class="module-icon-section">
                      <div class="module-icon-display" @click="openIconUploader(moduleIndex)">
                        <img v-if="module.icon" :src="module.icon" class="custom-icon" alt="模块图标" />
                        <div v-else class="default-icon-placeholder">
                          <i class="el-icon-plus"></i>
                          <span>上传图标</span>
                        </div>
                      </div>
                      <el-dropdown trigger="click" @command="handleIconCommand($event, moduleIndex)" v-if="module.icon">
                        <el-button type="text" size="mini" class="icon-edit-btn">
                          <i class="el-icon-edit"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item command="upload">重新上传</el-dropdown-item>
                          <el-dropdown-item command="remove">移除图标</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>

                    <el-input
                      v-model="module.name"
                      placeholder="请输入模块名称"
                      size="small"
                      class="module-name-input"
                      @blur="validateModuleName(module)"
                    />
                  </div>
                  <div class="module-actions">
                    <el-button type="text" size="mini" @click="moveModuleUp(moduleIndex)" :disabled="moduleIndex === 0">上移</el-button>
                    <el-button type="text" size="mini" @click="moveModuleDown(moduleIndex)" :disabled="moduleIndex === formModulesList.length - 1">下移</el-button>
                    <el-button type="text" size="mini" @click="addFieldToModule(moduleIndex)" icon="el-icon-plus">添加字段</el-button>
                    <el-button type="text" size="mini" @click="removeModule(moduleIndex)" style="color: #f56c6c;">删除模块</el-button>
                  </div>
                </div>



                <!-- 字段列表 -->
                <div class="fields-list" v-if="module.fields && module.fields.length > 0">
                  <div v-for="(field, fieldIndex) in module.fields" :key="fieldIndex" class="field-item">
                    <el-card shadow="hover" class="field-card">
                      <div class="field-header">
                        <span class="field-title">
                          {{ field.label || '未命名字段' }}
                          <el-tag v-if="field.hidden" size="mini" type="info" style="margin-left: 8px;">隐藏</el-tag>
                        </span>
                        <div class="field-actions">
                          <el-button type="text" size="mini" @click="moveFieldUpInModule(moduleIndex, fieldIndex)" :disabled="fieldIndex === 0">上移</el-button>
                          <el-button type="text" size="mini" @click="moveFieldDownInModule(moduleIndex, fieldIndex)" :disabled="fieldIndex === module.fields.length - 1">下移</el-button>
                          <el-button type="text" size="mini" @click="removeFieldFromModule(moduleIndex, fieldIndex)" style="color: #f56c6c;">删除</el-button>
                        </div>
                      </div>

                      <el-form :model="field" label-width="80px" size="small">
                        <el-form-item label="字段标签">
                          <el-input v-model="field.label" placeholder="请输入字段标签" @input="generateFieldName(field)" />
                        </el-form-item>
                        <el-form-item label="字段类型">
                          <el-select v-model="field.type" placeholder="请选择字段类型" @change="onFieldTypeChange(field)">
                            <el-option label="文本输入" value="input" />
                            <el-option label="多行文本" value="textarea" />
                            <el-option label="数字输入" value="number" />
                            <el-option label="电话号码" value="tel" />
                            <el-option label="邮箱地址" value="email" />
                            <el-option label="单选框" value="radio" />
                            <el-option label="多选框" value="checkbox" />
                            <el-option label="下拉选择" value="select" />
                            <el-option label="日期选择" value="date" />
                            <el-option label="时间选择" value="time" />
                            <el-option label="文件上传" value="file" />
                            <el-option label="静态展示" value="static" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="提示文字" v-if="field.type !== 'static'">
                          <el-input v-model="field.placeholder" placeholder="请输入提示文字，如：请输入姓名" />
                        </el-form-item>
                        <el-form-item label="显示内容" v-if="field.type === 'static'">
                          <el-input v-model="field.staticContent" type="textarea" placeholder="请输入要显示的静态内容" :rows="3" />
                        </el-form-item>
                        <el-form-item label="是否必填" v-if="field.type !== 'static'">
                          <el-switch v-model="field.required" />
                        </el-form-item>
                        <el-form-item label="是否隐藏">
                          <el-switch v-model="field.hidden" @change="onFieldHiddenChange(field)" />
                          <div class="field-tip" v-if="field.hidden">
                            <i class="el-icon-info"></i>
                            <span>隐藏的字段在小程序端不会显示给用户</span>
                          </div>
                        </el-form-item>
                        <el-form-item label="选项配置" v-if="['radio', 'checkbox', 'select'].includes(field.type)">
                          <el-input v-model="field.options" type="textarea" placeholder="请输入选项，用逗号分隔" :rows="2" />
                        </el-form-item>
                      </el-form>
                    </el-card>
                  </div>
                </div>

                <!-- 空模块提示 -->
                <div v-else class="empty-module">
                  <el-empty description="暂无字段" :image-size="60">
                    <el-button type="primary" size="small" @click="addFieldToModule(moduleIndex)">添加字段</el-button>
                  </el-empty>
                </div>
              </el-card>
            </div>

            <!-- 空状态 -->
            <div v-if="formModulesList.length === 0" class="empty-modules">
              <el-empty description="暂无模块" :image-size="80">
                <el-button type="primary" @click="addFormModule">创建第一个模块</el-button>
              </el-empty>
            </div>
          </div>
        </div>

        <!-- 右侧预览区域 -->
        <div class="preview-area">
          <div class="preview-header">
            <h4>表单预览</h4>
            <el-button type="success" size="small" @click="previewForm">预览表单</el-button>
          </div>
          <div class="preview-content">
            <div v-for="(module, moduleIndex) in formModulesList" :key="moduleIndex" class="preview-module">
              <div class="preview-module-header">
                <div class="preview-module-title">
                  <img v-if="module.icon" :src="module.icon" class="preview-module-icon-img" alt="模块图标" />
                  <i v-else class="el-icon-folder-opened preview-module-icon"></i>
                  <h5>{{ module.name || '未命名模块' }}</h5>
                </div>
              </div>
              <el-form label-width="100px" size="small" v-if="module.fields && module.fields.length > 0">
                <el-form-item v-for="field in module.fields" :key="field.name" :label="field.label" :required="field.required && field.type !== 'static'" :class="{ 'hidden-field': field.hidden }">
                  <div v-if="field.hidden" class="hidden-field-indicator">
                    <el-tag size="mini" type="info">隐藏字段</el-tag>
                  </div>
                  <!-- 静态展示 -->
                  <div v-if="field.type === 'static'" class="static-content">
                    {{ field.staticContent || '暂无内容' }}
                  </div>
                  <!-- 文本输入 -->
                  <el-input v-else-if="field.type === 'input'" v-model="previewData[field.name]" :placeholder="field.placeholder || '请输入'" disabled />
                  <!-- 多行文本 -->
                  <el-input v-else-if="field.type === 'textarea'" v-model="previewData[field.name]" type="textarea" :placeholder="field.placeholder || '请输入'" disabled />
                  <!-- 数字输入 -->
                  <el-input-number v-else-if="field.type === 'number'" v-model="previewData[field.name]" :placeholder="field.placeholder || '请输入数字'" disabled />
                  <!-- 电话号码 -->
                  <el-input v-else-if="field.type === 'tel'" v-model="previewData[field.name]" :placeholder="field.placeholder || '请输入电话号码'" disabled />
                  <!-- 邮箱地址 -->
                  <el-input v-else-if="field.type === 'email'" v-model="previewData[field.name]" :placeholder="field.placeholder || '请输入邮箱地址'" disabled />
                  <!-- 单选框 -->
                  <el-radio-group v-else-if="field.type === 'radio'" v-model="previewData[field.name]" disabled>
                    <el-radio v-for="option in getFieldOptions(field)" :key="option" :label="option">{{ option }}</el-radio>
                  </el-radio-group>
                  <!-- 多选框 -->
                  <el-checkbox-group v-else-if="field.type === 'checkbox'" v-model="previewData[field.name]" disabled>
                    <el-checkbox v-for="option in getFieldOptions(field)" :key="option" :label="option">{{ option }}</el-checkbox>
                  </el-checkbox-group>
                  <!-- 下拉选择 -->
                  <el-select v-else-if="field.type === 'select'" v-model="previewData[field.name]" :placeholder="field.placeholder || '请选择'" disabled>
                    <el-option v-for="option in getFieldOptions(field)" :key="option" :label="option" :value="option" />
                  </el-select>
                  <!-- 日期选择 -->
                  <el-date-picker v-else-if="field.type === 'date'" v-model="previewData[field.name]" type="date" :placeholder="field.placeholder || '请选择日期'" disabled />
                  <!-- 时间选择 -->
                  <el-time-picker v-else-if="field.type === 'time'" v-model="previewData[field.name]" :placeholder="field.placeholder || '请选择时间'" disabled />
                  <!-- 文件上传 -->
                  <el-upload v-else-if="field.type === 'file'" action="#" disabled>
                    <el-button size="small" type="primary" disabled>{{ field.placeholder || '点击上传' }}</el-button>
                  </el-upload>
                </el-form-item>
              </el-form>
              <div v-else class="preview-empty-module">
                <span>该模块暂无字段</span>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="formModulesList.length === 0" class="preview-empty">
              <el-empty description="暂无表单配置" :image-size="60" />
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveFormConfig">保存配置</el-button>
        <el-button @click="cancelFormConfig">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 表单预览对话框 -->
    <el-dialog title="表单预览" :visible.sync="previewOpen" width="800px" append-to-body>
      <div class="preview-dialog-content">
        <div v-for="(module, moduleIndex) in formModulesList" :key="moduleIndex" class="preview-dialog-module">
          <div class="preview-dialog-module-header">
            <div class="preview-dialog-module-title">
              <img v-if="module.icon" :src="module.icon" class="preview-dialog-module-icon-img" alt="模块图标" />
              <i v-else class="el-icon-folder-opened preview-dialog-module-icon"></i>
              <h4>{{ module.name || '未命名模块' }}</h4>
            </div>
          </div>
          <el-form label-width="100px" v-if="module.fields && module.fields.length > 0">
            <el-form-item v-for="field in module.fields" :key="field.name" :label="field.label" :required="field.required && field.type !== 'static'" :class="{ 'hidden-field': field.hidden }">
              <div v-if="field.hidden" class="hidden-field-indicator">
                <el-tag size="mini" type="info">隐藏字段</el-tag>
              </div>
              <!-- 静态展示 -->
              <div v-if="field.type === 'static'" class="static-content">
                {{ field.staticContent || '暂无内容' }}
              </div>
              <!-- 文本输入 -->
              <el-input v-else-if="field.type === 'input'" v-model="previewDialogData[field.name]" :placeholder="field.placeholder || '请输入'" />
              <!-- 多行文本 -->
              <el-input v-else-if="field.type === 'textarea'" v-model="previewDialogData[field.name]" type="textarea" :placeholder="field.placeholder || '请输入'" />
              <!-- 数字输入 -->
              <el-input-number v-else-if="field.type === 'number'" v-model="previewDialogData[field.name]" :placeholder="field.placeholder || '请输入数字'" />
              <!-- 电话号码 -->
              <el-input v-else-if="field.type === 'tel'" v-model="previewDialogData[field.name]" :placeholder="field.placeholder || '请输入电话号码'" />
              <!-- 邮箱地址 -->
              <el-input v-else-if="field.type === 'email'" v-model="previewDialogData[field.name]" :placeholder="field.placeholder || '请输入邮箱地址'" />
              <!-- 单选框 -->
              <el-radio-group v-else-if="field.type === 'radio'" v-model="previewDialogData[field.name]">
                <el-radio v-for="option in getFieldOptions(field)" :key="option" :label="option">{{ option }}</el-radio>
              </el-radio-group>
              <!-- 多选框 -->
              <el-checkbox-group v-else-if="field.type === 'checkbox'" v-model="previewDialogData[field.name]">
                <el-checkbox v-for="option in getFieldOptions(field)" :key="option" :label="option">{{ option }}</el-checkbox>
              </el-checkbox-group>
              <!-- 下拉选择 -->
              <el-select v-else-if="field.type === 'select'" v-model="previewDialogData[field.name]" :placeholder="field.placeholder || '请选择'">
                <el-option v-for="option in getFieldOptions(field)" :key="option" :label="option" :value="option" />
              </el-select>
              <!-- 日期选择 -->
              <el-date-picker v-else-if="field.type === 'date'" v-model="previewDialogData[field.name]" type="date" :placeholder="field.placeholder || '请选择日期'" />
              <!-- 时间选择 -->
              <el-time-picker v-else-if="field.type === 'time'" v-model="previewDialogData[field.name]" :placeholder="field.placeholder || '请选择时间'" />
              <!-- 文件上传 -->
              <el-upload v-else-if="field.type === 'file'" action="#">
                <el-button size="small" type="primary">{{ field.placeholder || '点击上传' }}</el-button>
              </el-upload>
            </el-form-item>
          </el-form>
          <div v-else class="preview-dialog-empty-module">
            <span>该模块暂无字段</span>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="formModulesList.length === 0" class="preview-dialog-empty">
          <el-empty description="暂无表单配置" :image-size="60" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 图标上传对话框 -->
    <el-dialog title="上传模块图标" :visible.sync="iconUploaderOpen" width="500px" append-to-body>
      <div class="icon-uploader-content">
        <div class="upload-section">
          <h4>上传自定义图标</h4>
          <ImageUpload
            v-model="uploadedIcon"
            :limit="1"
            :fileSize="2"
            :isShowTip="true"
          />
          <div class="upload-tips">
            <p>• 支持 jpg、png、gif 格式</p>
            <p>• 文件大小不超过 2MB</p>
            <p>• 建议尺寸 32x32 像素，保证清晰度</p>
            <p>• 建议使用透明背景的PNG格式</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelIconUpload">取 消</el-button>
        <el-button type="primary" @click="confirmIconUpload" :disabled="!uploadedIcon">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDemandCategory, getDemandCategory, delDemandCategory, addDemandCategory, updateDemandCategory } from "@/api/miniapp/demandcategory";
import { isExternal } from "@/utils/validate";

export default {
  name: "MiniDemandCategory",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 需求类型表格数据
      categoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: "类型名称不能为空", trigger: "blur" }
        ],
        categoryCode: [
          { required: true, message: "类型标识不能为空", trigger: "blur" },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: "类型标识必须以字母开头，只能包含字母、数字和下划线", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      // 表单配置相关
      formConfigOpen: false,
      previewOpen: false,
      currentCategoryId: null,
      formFieldsList: [], // 保留兼容性
      formModulesList: [], // 新的模块化结构
      selectedTemplate: '',
      previewData: {}, // 预览区域的表单数据
      previewDialogData: {}, // 预览对话框的表单数据
      // 图标相关
      iconUploaderOpen: false,
      currentModuleIndex: -1,
      uploadedIcon: ''
    };
  },
  created() {
    this.getList();
  },
  methods: {

    /** 查询需求类型列表 */
    getList() {
      this.loading = true;
      listDemandCategory(this.queryParams).then(response => {
        this.categoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        categoryId: null,
        categoryName: null,
        categoryCode: null,
        categoryShortName: null,
        categoryIcon: null,
        categoryDesc: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.categoryId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加需求类型";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const categoryId = row.categoryId || this.ids;
      getDemandCategory(categoryId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改需求类型";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.categoryId != null) {
            updateDemandCategory(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDemandCategory(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const categoryIds = row.categoryId || this.ids;
      this.$modal.confirm('是否确认删除需求类型编号为"' + categoryIds + '"的数据项？').then(function() {
        return delDemandCategory(categoryIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/demandcategory/export', {
        ...this.queryParams
      }, `需求类型数据_${new Date().getTime()}.xlsx`)
    },
    /** 排序修改 */
    handleSortChange(row) {
      updateDemandCategory(row).then(response => {
        this.$modal.msgSuccess("排序修改成功");
        this.getList();
      });
    },

    /** 表单配置按钮操作 */
    handleFormConfig(row) {
      this.currentCategoryId = row.categoryId;
      this.formFieldsList = [];
      this.formModulesList = [];

      // 如果已有配置，解析JSON
      if (row.formFields) {
        try {
          const parsedData = JSON.parse(row.formFields);

          // 检查是否为新的模块化结构
          if (Array.isArray(parsedData) && parsedData.length > 0 && parsedData[0].hasOwnProperty('name')) {
            // 新的模块化结构
            this.formModulesList = parsedData;
            // 确保所有字段都有 hidden 属性
            this.formModulesList.forEach(module => {
              if (module.fields) {
                module.fields.forEach(field => {
                  if (field.hidden === undefined) {
                    this.$set(field, 'hidden', false);
                  }
                });
              }
            });
          } else if (Array.isArray(parsedData)) {
            // 旧的字段列表结构，转换为模块化结构
            this.formFieldsList = parsedData;
            if (parsedData.length > 0) {
              // 为旧字段添加 hidden 属性
              parsedData.forEach(field => {
                if (field.hidden === undefined) {
                  this.$set(field, 'hidden', false);
                }
              });
              this.formModulesList = [{
                name: '基础信息',
                description: '',
                fields: parsedData
              }];
            }
          }
        } catch (e) {
          console.error('解析表单配置失败:', e);
          this.formFieldsList = [];
          this.formModulesList = [];
        }
      }

      this.formConfigOpen = true;
      this.initPreviewData();
    },

    /** 初始化预览数据 */
    initPreviewData() {
      this.previewData = {};
      this.formModulesList.forEach(module => {
        if (module.fields) {
          module.fields.forEach(field => {
            if (field.name) {
              if (field.type === 'checkbox') {
                this.previewData[field.name] = [];
              } else if (field.type === 'number') {
                this.previewData[field.name] = null;
              } else if (field.type === 'date' || field.type === 'time') {
                this.previewData[field.name] = null;
              } else {
                this.previewData[field.name] = '';
              }
            }
          });
        }
      });
    },

    /** 添加表单模块 */
    addFormModule() {
      const newModule = {
        name: '新模块',
        description: '',
        fields: [],
        icon: '' // 默认无图标，需要用户上传
      };
      this.formModulesList.push(newModule);
    },

    /** 删除模块 */
    removeModule(moduleIndex) {
      this.$confirm('确认删除该模块及其所有字段吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formModulesList.splice(moduleIndex, 1);
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    /** 模块上移 */
    moveModuleUp(moduleIndex) {
      if (moduleIndex > 0) {
        const temp = this.formModulesList[moduleIndex];
        this.$set(this.formModulesList, moduleIndex, this.formModulesList[moduleIndex - 1]);
        this.$set(this.formModulesList, moduleIndex - 1, temp);
      }
    },

    /** 模块下移 */
    moveModuleDown(moduleIndex) {
      if (moduleIndex < this.formModulesList.length - 1) {
        const temp = this.formModulesList[moduleIndex];
        this.$set(this.formModulesList, moduleIndex, this.formModulesList[moduleIndex + 1]);
        this.$set(this.formModulesList, moduleIndex + 1, temp);
      }
    },

    /** 验证模块名称 */
    validateModuleName(module) {
      if (!module.name || module.name.trim() === '') {
        module.name = '未命名模块';
      }
    },

    /** 向模块添加字段 */
    addFieldToModule(moduleIndex) {
      const newField = {
        label: '',
        name: '',
        type: 'input',
        required: false,
        hidden: false, // 默认不隐藏
        options: '',
        placeholder: '请输入',
        staticContent: ''
      };
      if (!this.formModulesList[moduleIndex].fields) {
        this.$set(this.formModulesList[moduleIndex], 'fields', []);
      }
      this.formModulesList[moduleIndex].fields.push(newField);
      // 更新预览数据
      this.$nextTick(() => {
        this.initPreviewData();
      });
    },

    /** 处理图标命令 */
    handleIconCommand(command, moduleIndex) {
      this.currentModuleIndex = moduleIndex;

      if (command === 'upload') {
        this.uploadedIcon = '';
        this.iconUploaderOpen = true;
      } else if (command === 'remove') {
        this.$set(this.formModulesList[moduleIndex], 'icon', '');
      }
    },

    /** 打开图标上传器 */
    openIconUploader(moduleIndex) {
      this.currentModuleIndex = moduleIndex;
      this.uploadedIcon = '';
      this.iconUploaderOpen = true;
    },

    /** 确认图标上传 */
    confirmIconUpload() {
      if (this.currentModuleIndex >= 0 && this.uploadedIcon) {
        this.$set(this.formModulesList[this.currentModuleIndex], 'icon', this.uploadedIcon);
        this.$message.success('图标上传成功');
      }
      this.iconUploaderOpen = false;
      this.currentModuleIndex = -1;
      this.uploadedIcon = '';
    },

    /** 取消图标上传 */
    cancelIconUpload() {
      this.iconUploaderOpen = false;
      this.currentModuleIndex = -1;
      this.uploadedIcon = '';
    },

    /** 字段类型变化时的处理 */
    onFieldTypeChange(field) {
      // 根据字段类型设置默认的placeholder
      const placeholderMap = {
        'input': '请输入',
        'textarea': '请输入',
        'number': '请输入数字',
        'tel': '请输入电话号码',
        'email': '请输入邮箱地址',
        'radio': '',
        'checkbox': '',
        'select': '请选择',
        'date': '请选择日期',
        'time': '请选择时间',
        'file': '点击上传',
        'static': ''
      };

      if (!field.placeholder || field.placeholder === '') {
        field.placeholder = placeholderMap[field.type] || '请输入';
      }

      // 如果是静态展示字段，设置默认内容和清除必填状态
      if (field.type === 'static') {
        field.required = false;
        if (!field.staticContent) {
          field.staticContent = '这里是静态展示内容';
        }
      }
    },

    /** 字段隐藏状态变化时的处理 */
    onFieldHiddenChange() {
      // 触发视图更新，无需额外处理
    },

    /** 从模块删除字段 */
    removeFieldFromModule(moduleIndex, fieldIndex) {
      this.formModulesList[moduleIndex].fields.splice(fieldIndex, 1);
    },

    /** 模块内字段上移 */
    moveFieldUpInModule(moduleIndex, fieldIndex) {
      const fields = this.formModulesList[moduleIndex].fields;
      if (fieldIndex > 0) {
        const temp = fields[fieldIndex];
        this.$set(fields, fieldIndex, fields[fieldIndex - 1]);
        this.$set(fields, fieldIndex - 1, temp);
      }
    },

    /** 模块内字段下移 */
    moveFieldDownInModule(moduleIndex, fieldIndex) {
      const fields = this.formModulesList[moduleIndex].fields;
      if (fieldIndex < fields.length - 1) {
        const temp = fields[fieldIndex];
        this.$set(fields, fieldIndex, fields[fieldIndex + 1]);
        this.$set(fields, fieldIndex + 1, temp);
      }
    },

    /** 添加表单字段（兼容旧方法） */
    addFormField() {
      // 如果没有模块，先创建一个默认模块
      if (this.formModulesList.length === 0) {
        this.addFormModule();
        this.formModulesList[0].name = '基础信息';
      }

      // 添加到第一个模块
      this.addFieldToModule(0);
    },



    /** 生成字段名称 */
    generateFieldName(field) {
      if (field.label) {
        // 扩展的中文转英文映射
        const nameMap = {
          // 基础信息
          '姓名': 'name',
          '联系人': 'contact_name',
          '联系电话': 'phone',
          '手机号': 'phone',
          '电话': 'phone',
          '邮箱': 'email',
          '邮箱地址': 'email',
          '公司': 'company',
          '公司名称': 'company_name',
          '职位': 'position',
          '部门': 'department',
          '地址': 'address',
          '详细地址': 'detailed_address',

          // 技术相关
          '技术方向': 'tech_direction',
          '技术栈': 'tech_stack',
          '开发语言': 'programming_language',
          '项目周期': 'project_duration',
          '预算范围': 'budget_range',
          '项目详细需求': 'detailed_requirements',
          '技术要求': 'tech_requirements',

          // 市场推广相关
          '推广类型': 'promotion_type',
          '目标客户群体': 'target_audience',
          '推广渠道': 'promotion_channels',
          '推广预算': 'promotion_budget',
          '推广时间': 'promotion_duration',
          '推广目标': 'promotion_goals',

          // 招聘相关
          '招聘职位': 'job_position',
          '工作经验': 'work_experience',
          '学历要求': 'education_requirement',
          '薪资范围': 'salary_range',
          '工作地点': 'work_location',
          '职位描述': 'job_description',

          // 投资相关
          '投资类型': 'investment_type',
          '投资金额': 'investment_amount',
          '投资阶段': 'investment_stage',
          '行业领域': 'industry_field',
          '项目介绍': 'project_introduction',

          // 采购相关
          '产品名称': 'product_name',
          '采购数量': 'purchase_quantity',
          '质量要求': 'quality_requirements',
          '交付时间': 'delivery_time',
          '采购预算': 'purchase_budget',

          // 通用字段
          '需求描述': 'description',
          '详细说明': 'detailed_description',
          '备注': 'remark',
          '说明': 'note',
          '标题': 'title',
          '内容': 'content',
          '时间': 'time',
          '日期': 'date',
          '文件': 'file',
          '图片': 'image',
          '附件': 'attachment'
        };

        // 如果有直接映射，使用映射值
        if (nameMap[field.label]) {
          field.name = nameMap[field.label];
        } else {
          // 否则进行智能转换
          let name = field.label
            .replace(/[\s\-\/\\]/g, '_') // 替换空格、横线、斜线为下划线
            .replace(/[^\w\u4e00-\u9fa5]/g, '') // 移除特殊字符，保留中英文和数字
            .toLowerCase();

          // 如果包含中文，尝试转换为拼音或英文
          if (/[\u4e00-\u9fa5]/.test(name)) {
            // 简单的中文关键词替换
            name = name
              .replace(/类型/g, 'type')
              .replace(/名称/g, 'name')
              .replace(/时间/g, 'time')
              .replace(/日期/g, 'date')
              .replace(/地址/g, 'address')
              .replace(/电话/g, 'phone')
              .replace(/邮箱/g, 'email')
              .replace(/公司/g, 'company')
              .replace(/描述/g, 'description')
              .replace(/说明/g, 'note')
              .replace(/备注/g, 'remark')
              .replace(/要求/g, 'requirement')
              .replace(/范围/g, 'range')
              .replace(/预算/g, 'budget')
              .replace(/数量/g, 'quantity')
              .replace(/价格/g, 'price')
              .replace(/费用/g, 'cost');

            // 如果还有中文，使用拼音首字母或保持原样
            if (/[\u4e00-\u9fa5]/.test(name)) {
              name = 'field_' + Date.now().toString().slice(-6); // 使用时间戳后6位作为唯一标识
            }
          }

          field.name = name;
        }
      }
    },

    /** 应用预设模板 */
    applyTemplate() {
      if (this.selectedTemplate === 'basic') {
        this.applyBasicTemplate();
      } else if (this.selectedTemplate === 'tech') {
        this.applyTechTemplate();
      } else if (this.selectedTemplate === 'business') {
        this.applyBusinessTemplate();
      }
      // 应用模板后初始化预览数据
      this.$nextTick(() => {
        this.initPreviewData();
      });
    },

    /** 应用基础需求模板 */
    applyBasicTemplate() {
      this.formModulesList = [
        {
          name: '填写说明',
          description: '请仔细阅读以下说明后填写表单',
          fields: [
            {
              label: '温馨提示',
              name: 'tips',
              type: 'static',
              required: false,
              hidden: false,
              options: '',
              placeholder: '',
              staticContent: '请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。'
            }
          ]
        },
        {
          name: '基础信息',
          description: '请填写需求的基本信息',
          fields: [
            {
              label: '需求标题',
              name: 'title',
              type: 'input',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入需求标题',
              staticContent: ''
            },
            {
              label: '需求描述',
              name: 'description',
              type: 'textarea',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请详细描述您的需求',
              staticContent: ''
            }
          ]
        },
        {
          name: '联系方式',
          description: '请填写您的联系方式，以便我们与您取得联系',
          fields: [
            {
              label: '联系人',
              name: 'contact_name',
              type: 'input',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入联系人姓名',
              staticContent: ''
            },
            {
              label: '联系电话',
              name: 'phone',
              type: 'tel',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入手机号码',
              staticContent: ''
            }
          ]
        }
      ];

      // 确保字段名称正确生成
      this.formModulesList.forEach(module => {
        module.fields.forEach(field => {
          this.generateFieldName(field);
        });
      });
    },

    /** 应用技术需求模板 */
    applyTechTemplate() {
      this.formModulesList = [
        {
          name: '技术需求',
          description: '请详细描述您的技术需求',
          fields: [
            {
              label: '技术方向',
              name: '',
              type: 'select',
              required: true,
              hidden: false,
              options: '前端开发,后端开发,移动开发,人工智能,大数据,云计算',
              placeholder: '请选择技术方向',
              staticContent: ''
            },
            {
              label: '技术栈',
              name: '',
              type: 'checkbox',
              required: false,
              hidden: false,
              options: 'Java,Python,JavaScript,React,Vue,Spring Boot,MySQL,Redis',
              placeholder: '',
              staticContent: ''
            },
            {
              label: '项目详细需求',
              name: '',
              type: 'textarea',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请详细描述项目需求、功能要求、技术要求等',
              staticContent: ''
            }
          ]
        },
        {
          name: '项目信息',
          description: '请填写项目的基本信息',
          fields: [
            {
              label: '项目周期',
              name: '',
              type: 'select',
              required: true,
              hidden: false,
              options: '1周内,1-2周,2-4周,1-2个月,2-3个月,3个月以上',
              placeholder: '请选择项目周期',
              staticContent: ''
            },
            {
              label: '预算范围',
              name: '',
              type: 'radio',
              required: true,
              hidden: false,
              options: '1万以下,1-5万,5-10万,10-20万,20万以上',
              placeholder: '',
              staticContent: ''
            }
          ]
        },
        {
          name: '联系方式',
          description: '请填写您的联系方式',
          fields: [
            {
              label: '联系人',
              name: '',
              type: 'input',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入联系人姓名',
              staticContent: ''
            },
            {
              label: '联系电话',
              name: '',
              type: 'tel',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入手机号码',
              staticContent: ''
            }
          ]
        }
      ];

      // 自动生成字段名称
      this.formModulesList.forEach(module => {
        module.fields.forEach(field => {
          this.generateFieldName(field);
        });
      });
    },

    /** 应用商务合作模板 */
    applyBusinessTemplate() {
      this.formModulesList = [
        {
          name: '合作信息',
          description: '请填写合作相关信息',
          fields: [
            {
              label: '合作类型',
              name: '',
              type: 'radio',
              required: true,
              hidden: false,
              options: '战略合作,技术合作,市场合作,投资合作',
              placeholder: '',
              staticContent: ''
            },
            {
              label: '合作描述',
              name: '',
              type: 'textarea',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请详细描述合作内容、合作方式、期望达成的目标等',
              staticContent: ''
            }
          ]
        },
        {
          name: '公司信息',
          description: '请填写您的公司基本信息',
          fields: [
            {
              label: '公司名称',
              name: '',
              type: 'input',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入公司全称',
              staticContent: ''
            },
            {
              label: '公司规模',
              name: '',
              type: 'select',
              required: true,
              hidden: false,
              options: '10人以下,10-50人,50-200人,200-500人,500人以上',
              placeholder: '请选择公司规模',
              staticContent: ''
            },
            {
              label: '行业领域',
              name: '',
              type: 'select',
              required: true,
              hidden: false,
              options: '互联网,金融,教育,医疗,制造业,服务业,其他',
              placeholder: '请选择行业领域',
              staticContent: ''
            }
          ]
        },
        {
          name: '联系方式',
          description: '请填写联系方式，以便我们与您取得联系',
          fields: [
            {
              label: '联系人',
              name: '',
              type: 'input',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入联系人姓名',
              staticContent: ''
            },
            {
              label: '联系电话',
              name: '',
              type: 'tel',
              required: true,
              hidden: false,
              options: '',
              placeholder: '请输入手机号码',
              staticContent: ''
            },
            {
              label: '邮箱地址',
              name: '',
              type: 'email',
              required: false,
              hidden: false,
              options: '',
              placeholder: '请输入邮箱地址（选填）',
              staticContent: ''
            }
          ]
        }
      ];

      // 自动生成字段名称
      this.formModulesList.forEach(module => {
        module.fields.forEach(field => {
          this.generateFieldName(field);
        });
      });
    },

    /** 获取字段选项 */
    getFieldOptions(field) {
      if (!field.options || field.options.trim() === '') return [];
      return field.options.split(',').map(option => option.trim()).filter(option => option !== '');
    },

    /** 预览表单 */
    previewForm() {
      this.initPreviewDialogData();
      this.previewOpen = true;
    },

    /** 初始化预览对话框数据 */
    initPreviewDialogData() {
      this.previewDialogData = {};
      this.formModulesList.forEach(module => {
        if (module.fields) {
          module.fields.forEach(field => {
            if (field.name) {
              if (field.type === 'checkbox') {
                this.previewDialogData[field.name] = [];
              } else if (field.type === 'number') {
                this.previewDialogData[field.name] = null;
              } else if (field.type === 'date' || field.type === 'time') {
                this.previewDialogData[field.name] = null;
              } else {
                this.previewDialogData[field.name] = '';
              }
            }
          });
        }
      });
    },

    /** 保存表单配置 */
    saveFormConfig() {
      if (!this.currentCategoryId) {
        this.$modal.msgError("未选择需求类型");
        return;
      }

      // 验证模块配置
      for (let i = 0; i < this.formModulesList.length; i++) {
        const module = this.formModulesList[i];
        if (!module.name || module.name.trim() === '') {
          this.$modal.msgError(`第${i + 1}个模块名称不能为空`);
          return;
        }

        for (let j = 0; j < module.fields.length; j++) {
          const field = module.fields[j];
          if (!field.label || field.label.trim() === '') {
            this.$modal.msgError(`模块"${module.name}"中第${j + 1}个字段标签不能为空`);
            return;
          }
        }
      }

      const formData = {
        categoryId: this.currentCategoryId,
        formFields: JSON.stringify(this.formModulesList)
      };

      updateDemandCategory(formData).then(() => {
        this.$modal.msgSuccess("表单配置保存成功");
        this.formConfigOpen = false;
        this.getList();
      }).catch(error => {
        console.error('保存表单配置失败:', error);
        this.$modal.msgError("保存表单配置失败");
      });
    },

    /** 取消表单配置 */
    cancelFormConfig() {
      this.formConfigOpen = false;
      this.formFieldsList = [];
      this.formModulesList = [];
      this.currentCategoryId = null;
      this.selectedTemplate = '';
      this.previewData = {};
      this.previewDialogData = {};
    }
  }
};
</script>

<style scoped>
.form-config-container {
  display: flex;
  height: 700px;
  gap: 20px;
}

.config-area {
  flex: 1;
  border-right: 1px solid #e6e6e6;
  padding-right: 20px;
  overflow-y: auto;
}

.preview-area {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
}

.config-header, .preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}

.config-header h4, .preview-header h4 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.template-section {
  margin-bottom: 20px;
}

.modules-list {
  max-height: 600px;
  overflow-y: auto;
}

.module-item {
  margin-bottom: 20px;
}

.module-card {
  border: 2px solid #e6e6e6;
  transition: border-color 0.3s;
}

.module-card:hover {
  border-color: #409EFF;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.module-title-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.module-icon {
  color: #409EFF;
  font-size: 18px;
  margin-right: 8px;
}

.module-name-input {
  max-width: 200px;
  margin-right: 10px;
}

.module-actions {
  display: flex;
  gap: 5px;
  flex-shrink: 0;
}



.fields-list {
  margin-top: 10px;
}

.field-item {
  margin-bottom: 15px;
}

.field-card {
  border: 1px solid #e6e6e6;
  margin-left: 20px;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.field-title {
  font-weight: bold;
  color: #606266;
}

.field-actions {
  display: flex;
  gap: 5px;
}

.empty-module {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.empty-modules {
  text-align: center;
  padding: 40px;
}

.preview-content {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
  min-height: 500px;
}

.preview-module {
  margin-bottom: 30px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-module-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409EFF;
}

.preview-module-header h5 {
  margin: 0 0 5px 0;
  color: #409EFF;
  font-size: 16px;
  font-weight: bold;
}



.preview-empty-module {
  text-align: center;
  color: #c0c4cc;
  padding: 20px;
  font-style: italic;
}

.preview-empty {
  text-align: center;
  padding: 40px;
}

/* 隐藏字段样式 */
.hidden-field {
  opacity: 0.6;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  border-left: 3px solid #909399;
}

.hidden-field-indicator {
  margin-bottom: 8px;
}

.field-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.field-tip i {
  margin-right: 4px;
}

/* 表单对话框样式优化 */
.el-dialog__body {
  padding: 20px 30px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-input-number {
  width: 100%;
}

/* 预览对话框样式 */
.preview-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.preview-dialog-module {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background-color: #fafafa;
}

.preview-dialog-module-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409EFF;
}

.preview-dialog-module-header h4 {
  margin: 0 0 5px 0;
  color: #409EFF;
  font-size: 16px;
}



.preview-dialog-empty-module {
  text-align: center;
  color: #c0c4cc;
  padding: 20px;
  font-style: italic;
}

.preview-dialog-empty {
  text-align: center;
  padding: 40px;
}

.el-card {
  margin-bottom: 10px;
}

.el-form-item {
  margin-bottom: 15px;
}

/* 静态展示内容样式 */
.static-content {
  padding: 10px 15px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 模块图标样式 */
.module-icon-section {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.module-icon-display {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.module-icon-display:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.custom-icon {
  width: 24px;
  height: 24px;
  object-fit: cover;
  border-radius: 2px;
}

.default-icon-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #909399;
  text-align: center;
}

.default-icon-placeholder i {
  font-size: 12px;
  margin-bottom: 2px;
}

.default-icon-placeholder span {
  line-height: 1;
}

.icon-edit-btn {
  padding: 4px !important;
  min-height: auto !important;
}

/* 图标上传器样式 */
.icon-uploader-content {
  text-align: center;
}

.upload-section h4 {
  margin-bottom: 20px;
  color: #303133;
}

.upload-tips {
  margin-top: 15px;
  text-align: left;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.upload-tips p {
  margin: 5px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

/* 预览区域图标样式 */
.preview-module-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-module-icon {
  font-size: 16px;
  color: #409eff;
}

.preview-module-icon-img {
  width: 16px;
  height: 16px;
  object-fit: cover;
}

.preview-dialog-module-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-dialog-module-icon {
  font-size: 18px;
  color: #409eff;
}

.preview-dialog-module-icon-img {
  width: 18px;
  height: 18px;
  object-fit: cover;
}

/* 类型图标样式 */
.category-icon {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.no-icon {
  color: #c0c4cc;
  font-size: 12px;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.form-tip p {
  margin: 2px 0;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.form-tip p {
  margin: 2px 0;
}

</style>
