{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1754297842992}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_validate", "_sortablejs", "_interopRequireDefault", "props", "value", "String", "Object", "Array", "action", "type", "default", "data", "limit", "Number", "fileSize", "fileType", "isShowTip", "Boolean", "disabled", "drag", "number", "uploadList", "dialogImageUrl", "dialogVisible", "hideUpload", "baseUrl", "process", "env", "VUE_APP_BASE_API", "uploadImgUrl", "headers", "Authorization", "getToken", "fileList", "mounted", "_this", "$nextTick", "_this$$refs$imageUplo", "element", "$refs", "imageUpload", "$el", "querySelector", "Sortable", "create", "onEnd", "evt", "movedItem", "splice", "oldIndex", "newIndex", "$emit", "listToString", "watch", "handler", "val", "_this2", "list", "isArray", "split", "map", "item", "indexOf", "isExternal", "fullUrl", "window", "location", "protocol", "host", "name", "url", "deep", "immediate", "computed", "showTip", "methods", "handleBeforeUpload", "file", "isImg", "length", "fileExtension", "lastIndexOf", "slice", "some", "$modal", "msgError", "concat", "join", "includes", "isLt", "size", "loading", "handleExceed", "handleUploadSuccess", "res", "code", "push", "fileName", "uploadedSuccessfully", "closeLoading", "msg", "handleRemove", "handleDelete", "findex", "f", "handleUploadError", "handlePictureCardPreview", "separator", "strs", "i", "substring"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :disabled=\"disabled\"\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :data=\"data\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip && !disabled\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\"\r\nimport { isExternal } from \"@/utils/validate\"\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 上传接口地址\r\n    action: {\r\n      type: String,\r\n      default: \"/common/upload\"\r\n    },\r\n    // 上传携带的参数\r\n    data: {\r\n      type: Object\r\n    },\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"]\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 禁用组件（仅查看图片）\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 拖动排序\r\n    drag: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.drag && !this.disabled) {\r\n      this.$nextTick(() => {\r\n        const element = this.$refs.imageUpload?.$el?.querySelector('.el-upload-list')\r\n        Sortable.create(element, {\r\n          onEnd: (evt) => {\r\n            const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]\r\n            this.fileList.splice(evt.newIndex, 0, movedItem)\r\n            this.$emit(\"input\", this.listToString(this.fileList))\r\n          }\r\n        })\r\n      })\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',')\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\r\n                  // 构建完整的URL：协议+主机+端口+API前缀+文件路径\r\n                  const fullUrl = window.location.protocol + '//' + window.location.host + this.baseUrl + item\r\n                  item = { name: fullUrl, url: fullUrl }\r\n              } else {\r\n                  item = { name: item, url: item }\r\n              }\r\n            }\r\n            return item\r\n          })\r\n        } else {\r\n          this.fileList = []\r\n          return []\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize)\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\"\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1)\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true\r\n          return false\r\n        })\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`)\r\n        return false\r\n      }\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!')\r\n        return false\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)\r\n          return false\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\")\r\n      this.number++\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        // fileName，相对路径\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName })\r\n        this.uploadedSuccessfully()\r\n      } else {\r\n        this.number--\r\n        this.$modal.closeLoading()\r\n        this.$modal.msgError(res.msg)\r\n        this.$refs.imageUpload.handleRemove(file)\r\n        this.uploadedSuccessfully()\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name)\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1)\r\n        this.$emit(\"input\", this.listToString(this.fileList))\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\")\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList)\r\n        this.uploadList = []\r\n        this.number = 0\r\n        this.$emit(\"input\", this.listToString(this.fileList))\r\n        this.$modal.closeLoading()\r\n      }\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\"\r\n      separator = separator || \",\"\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          // 保存完整的URL，不移除baseUrl前缀\r\n          strs += list[i].url + separator\r\n        }\r\n      }\r\n      return strs != '' ? strs.substring(0, strs.length - 1) : ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n  display: none;\r\n}\r\n\r\n::v-deep .el-upload-list--picture-card.is-disabled + .el-upload--picture-card {\r\n  display: none !important;\r\n}\r\n\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n  transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA+CA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAI,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAJ,MAAA;MACAK,OAAA;IACA;IACA;IACAC,IAAA;MACAF,IAAA,EAAAH;IACA;IACA;IACAM,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAI,QAAA;MACAL,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAK,QAAA;MACAN,IAAA,EAAAF,KAAA;MACAG,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAM,SAAA;MACAP,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAQ,QAAA;MACAT,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAS,IAAA;MACAV,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAS,MAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,YAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAApB,MAAA;MAAA;MACAsB,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,SAAAhB,IAAA,UAAAD,QAAA;MACA,KAAAkB,SAAA;QAAA,IAAAC,qBAAA;QACA,IAAAC,OAAA,IAAAD,qBAAA,GAAAF,KAAA,CAAAI,KAAA,CAAAC,WAAA,cAAAH,qBAAA,gBAAAA,qBAAA,GAAAA,qBAAA,CAAAI,GAAA,cAAAJ,qBAAA,uBAAAA,qBAAA,CAAAK,aAAA;QACAC,mBAAA,CAAAC,MAAA,CAAAN,OAAA;UACAO,KAAA,WAAAA,MAAAC,GAAA;YACA,IAAAC,SAAA,GAAAZ,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAG,QAAA;YACAd,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;YACAZ,KAAA,CAAAgB,KAAA,UAAAhB,KAAA,CAAAiB,YAAA,CAAAjB,KAAA,CAAAF,QAAA;UACA;QACA;MACA;IACA;EACA;EACAoB,KAAA;IACAjD,KAAA;MACAkD,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,GAAA;UACA;UACA,IAAAE,IAAA,GAAAlD,KAAA,CAAAmD,OAAA,CAAAH,GAAA,IAAAA,GAAA,QAAAnD,KAAA,CAAAuD,KAAA;UACA;UACA,KAAA1B,QAAA,GAAAwB,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACA,IAAAA,IAAA,CAAAC,OAAA,CAAAN,MAAA,CAAA/B,OAAA,iBAAAsC,oBAAA,EAAAF,IAAA;gBACA;gBACA,IAAAG,OAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,QAAA,UAAAF,MAAA,CAAAC,QAAA,CAAAE,IAAA,GAAAZ,MAAA,CAAA/B,OAAA,GAAAoC,IAAA;gBACAA,IAAA;kBAAAQ,IAAA,EAAAL,OAAA;kBAAAM,GAAA,EAAAN;gBAAA;cACA;gBACAH,IAAA;kBAAAQ,IAAA,EAAAR,IAAA;kBAAAS,GAAA,EAAAT;gBAAA;cACA;YACA;YACA,OAAAA,IAAA;UACA;QACA;UACA,KAAA5B,QAAA;UACA;QACA;MACA;MACAsC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAA1D,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACA6D,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA;MACA,SAAA/D,QAAA,CAAAgE,MAAA;QACA,IAAAC,aAAA;QACA,IAAAH,IAAA,CAAAR,IAAA,CAAAY,WAAA;UACAD,aAAA,GAAAH,IAAA,CAAAR,IAAA,CAAAa,KAAA,CAAAL,IAAA,CAAAR,IAAA,CAAAY,WAAA;QACA;QACAH,KAAA,QAAA/D,QAAA,CAAAoE,IAAA,WAAA1E,IAAA;UACA,IAAAoE,IAAA,CAAApE,IAAA,CAAAqD,OAAA,CAAArD,IAAA;UACA,IAAAuE,aAAA,IAAAA,aAAA,CAAAlB,OAAA,CAAArD,IAAA;UACA;QACA;MACA;QACAqE,KAAA,GAAAD,IAAA,CAAApE,IAAA,CAAAqD,OAAA;MACA;MAEA,KAAAgB,KAAA;QACA,KAAAM,MAAA,CAAAC,QAAA,sEAAAC,MAAA,MAAAvE,QAAA,CAAAwE,IAAA;QACA;MACA;MACA,IAAAV,IAAA,CAAAR,IAAA,CAAAmB,QAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA;QACA;MACA;MACA,SAAAvE,QAAA;QACA,IAAA2E,IAAA,GAAAZ,IAAA,CAAAa,IAAA,sBAAA5E,QAAA;QACA,KAAA2E,IAAA;UACA,KAAAL,MAAA,CAAAC,QAAA,6EAAAC,MAAA,MAAAxE,QAAA;UACA;QACA;MACA;MACA,KAAAsE,MAAA,CAAAO,OAAA;MACA,KAAAvE,MAAA;IACA;IACA;IACAwE,YAAA,WAAAA,aAAA;MACA,KAAAR,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAA1E,KAAA;IACA;IACA;IACAiF,mBAAA,WAAAA,oBAAAC,GAAA,EAAAjB,IAAA;MACA,IAAAiB,GAAA,CAAAC,IAAA;QACA;QACA,KAAA1E,UAAA,CAAA2E,IAAA;UAAA3B,IAAA,EAAAyB,GAAA,CAAAG,QAAA;UAAA3B,GAAA,EAAAwB,GAAA,CAAAG;QAAA;QACA,KAAAC,oBAAA;MACA;QACA,KAAA9E,MAAA;QACA,KAAAgE,MAAA,CAAAe,YAAA;QACA,KAAAf,MAAA,CAAAC,QAAA,CAAAS,GAAA,CAAAM,GAAA;QACA,KAAA7D,KAAA,CAAAC,WAAA,CAAA6D,YAAA,CAAAxB,IAAA;QACA,KAAAqB,oBAAA;MACA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAzB,IAAA;MACA,IAAA0B,MAAA,QAAAtE,QAAA,CAAA2B,GAAA,WAAA4C,CAAA;QAAA,OAAAA,CAAA,CAAAnC,IAAA;MAAA,GAAAP,OAAA,CAAAe,IAAA,CAAAR,IAAA;MACA,IAAAkC,MAAA;QACA,KAAAtE,QAAA,CAAAe,MAAA,CAAAuD,MAAA;QACA,KAAApD,KAAA,eAAAC,YAAA,MAAAnB,QAAA;MACA;IACA;IACA;IACAwE,iBAAA,WAAAA,kBAAA;MACA,KAAArB,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAe,YAAA;IACA;IACA;IACAD,oBAAA,WAAAA,qBAAA;MACA,SAAA9E,MAAA,aAAAC,UAAA,CAAA0D,MAAA,UAAA3D,MAAA;QACA,KAAAa,QAAA,QAAAA,QAAA,CAAAqD,MAAA,MAAAjE,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAA+B,KAAA,eAAAC,YAAA,MAAAnB,QAAA;QACA,KAAAmD,MAAA,CAAAe,YAAA;MACA;IACA;IACA;IACAO,wBAAA,WAAAA,yBAAA7B,IAAA;MACA,KAAAvD,cAAA,GAAAuD,IAAA,CAAAP,GAAA;MACA,KAAA/C,aAAA;IACA;IACA;IACA6B,YAAA,WAAAA,aAAAK,IAAA,EAAAkD,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAApD,IAAA;QACA,IAAAA,IAAA,CAAAoD,CAAA,EAAAvC,GAAA;UACA;UACAsC,IAAA,IAAAnD,IAAA,CAAAoD,CAAA,EAAAvC,GAAA,GAAAqC,SAAA;QACA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAE,SAAA,IAAAF,IAAA,CAAA7B,MAAA;IACA;EACA;AACA", "ignoreList": []}]}