{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=template&id=a19f53a8", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754295272977}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}