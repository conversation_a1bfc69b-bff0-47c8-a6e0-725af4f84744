<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniHomeModuleMapper">
    
    <resultMap type="MiniHomeModule" id="MiniHomeModuleResult">
        <result property="id"    column="id"    />
        <result property="moduleName"    column="module_name"    />
        <result property="moduleIcon"    column="module_icon"    />
        <result property="moduleCode"    column="module_code"    />
        <result property="moduleUrl"    column="module_url"    />
        <result property="linkType"    column="link_type"    />
        <result property="externalUrl"    column="external_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniHomeModuleVo">
        select id, module_name, module_icon, module_code, module_url, link_type, external_url, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_home_module
    </sql>

    <select id="selectMiniHomeModuleList" parameterType="MiniHomeModule" resultMap="MiniHomeModuleResult">
        <include refid="selectMiniHomeModuleVo"/>
        <where>
            <if test="moduleName != null  and moduleName != ''"> and module_name like concat('%', #{moduleName}, '%')</if>
            <if test="moduleCode != null  and moduleCode != ''"> and module_code = #{moduleCode}</if>
            <if test="linkType != null  and linkType != ''"> and link_type = #{linkType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniHomeModuleById" parameterType="Long" resultMap="MiniHomeModuleResult">
        <include refid="selectMiniHomeModuleVo"/>
        where id = #{id}
    </select>

    <select id="selectMiniHomeModuleByModuleCode" parameterType="String" resultMap="MiniHomeModuleResult">
        <include refid="selectMiniHomeModuleVo"/>
        where module_code = #{moduleCode}
    </select>

    <select id="selectActiveModuleList" resultMap="MiniHomeModuleResult">
        <include refid="selectMiniHomeModuleVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniHomeModule" parameterType="MiniHomeModule" useGeneratedKeys="true" keyProperty="id">
        insert into mini_home_module
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">module_name,</if>
            <if test="moduleIcon != null">module_icon,</if>
            <if test="moduleCode != null and moduleCode != ''">module_code,</if>
            <if test="moduleUrl != null">module_url,</if>
            <if test="linkType != null">link_type,</if>
            <if test="externalUrl != null">external_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
            <if test="moduleIcon != null">#{moduleIcon},</if>
            <if test="moduleCode != null and moduleCode != ''">#{moduleCode},</if>
            <if test="moduleUrl != null">#{moduleUrl},</if>
            <if test="linkType != null">#{linkType},</if>
            <if test="externalUrl != null">#{externalUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniHomeModule" parameterType="MiniHomeModule">
        update mini_home_module
        <trim prefix="SET" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="moduleIcon != null">module_icon = #{moduleIcon},</if>
            <if test="moduleCode != null and moduleCode != ''">module_code = #{moduleCode},</if>
            <if test="moduleUrl != null">module_url = #{moduleUrl},</if>
            <if test="linkType != null">link_type = #{linkType},</if>
            <if test="externalUrl != null">external_url = #{externalUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniHomeModuleById" parameterType="Long">
        delete from mini_home_module where id = #{id}
    </delete>

    <delete id="deleteMiniHomeModuleByIds" parameterType="String">
        delete from mini_home_module where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
